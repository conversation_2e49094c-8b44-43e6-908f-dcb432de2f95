import 'package:easydine_main/blocs/staff/staff_bloc.dart';
import 'package:easydine_main/blocs/staff/staff_event.dart';
import 'package:easydine_main/blocs/staff/staff_state.dart';
import 'package:easydine_main/blocs/attendance/attendance_bloc.dart';
import 'package:easydine_main/blocs/attendance/attendance_event.dart';
import 'package:easydine_main/blocs/attendance/attendance_state.dart';
import 'package:easydine_main/models/staff_model.dart';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

enum ClockAction { clockIn, clockOut }

class ClockActionPage extends StatefulWidget {
  final ClockAction action;

  const ClockActionPage({
    super.key,
    required this.action,
  });

  @override
  State<ClockActionPage> createState() => _ClockActionPageState();
}

class _ClockActionPageState extends State<ClockActionPage>
    with TickerProviderStateMixin {
  String _pin = '';
  bool _showError = false;
  StaffModel? _selectedStaff;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    context.read<AttendanceBloc>().add(const ResetAttendanceState());
    _fetchStaffData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onKeyPress(String digit) {
    if (_pin.length < 5) {
      setState(() {
        _pin += digit;
        _showError = false;
      });

      if (_pin.length == 5 && _selectedStaff != null) {
        if (widget.action == ClockAction.clockIn) {
          context.read<AttendanceBloc>().add(
                ClockInStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        } else {
          context.read<AttendanceBloc>().add(
                ClockOutStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        }
      }
    }
  }

  void _onBackspace() {
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
        _showError = false;
      });
    }
  }

  Future<void> _fetchStaffData() async {
    final branchId = await BranchService.getSelectedBranchId();
    if (branchId != null && mounted) {
      if (widget.action == ClockAction.clockOut) {
        context.read<StaffBloc>().add(FetchClockedInStaff(branchId: branchId));
      } else {
        context.read<StaffBloc>().add(FetchBranchStaff(branchId: branchId));
      }
    }
  }

  // New widget components for the redesigned page
  Widget _buildTimeTrackingHeader() {
    final isClockIn = widget.action == ClockAction.clockIn;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: isLandscape ? 3.w : 4.w,
                vertical: isLandscape ? 2.h : 2.5.h,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isClockIn
                      ? [
                          const Color(0xFF4CAF50),
                          const Color(0xFF2E7D32),
                        ]
                      : [
                          const Color(0xFFFF9800),
                          const Color(0xFFE65100),
                        ],
                ),
                borderRadius: BorderRadius.circular(isLandscape ? 2.w : 3.w),
                boxShadow: [
                  BoxShadow(
                    color: (isClockIn ? Colors.green : Colors.orange)
                        .withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: StreamBuilder<DateTime>(
                stream: Stream.periodic(
                  const Duration(seconds: 1),
                  (_) => DateTime.now(),
                ),
                builder: (context, snapshot) {
                  final now = snapshot.data ?? DateTime.now();

                  return isLandscape
                      ? Row(
                          children: [
                            // Left side - Action info
                            Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(1.5.w),
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      isClockIn
                                          ? Icons.access_time
                                          : Icons.timer_off,
                                      size: 5.w,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(width: 2.w),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        isClockIn ? 'Clock In' : 'Clock Out',
                                        style: GoogleFonts.poppins(
                                          fontSize: 15.sp,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                      Text(
                                        isClockIn
                                            ? 'Start Your Day'
                                            : 'End Your Shift',
                                        style: GoogleFonts.poppins(
                                          fontSize: 13.sp,
                                          color: Colors.white
                                              .withValues(alpha: 0.8),
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Center divider
                            Container(
                              height: 6.h,
                              width: 1,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),

                            // Right side - Date and time
                            Expanded(
                              flex: 3,
                              child: Padding(
                                padding: EdgeInsets.only(left: 3.w),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.calendar_today,
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                          size: 3.w,
                                        ),
                                        SizedBox(width: 1.w),
                                        Text(
                                          DateFormat('EEEE, MMM dd, yyyy')
                                              .format(now),
                                          style: GoogleFonts.poppins(
                                            fontSize: 12.sp,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 0.5.h),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.schedule,
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                          size: 3.w,
                                        ),
                                        SizedBox(width: 1.w),
                                        Text(
                                          DateFormat('HH:mm:ss').format(now),
                                          style: GoogleFonts.poppins(
                                            fontSize: 12.sp,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            letterSpacing: 1,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                      : Column(
                          children: [
                            // Top row - Action info and time
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Left - Action info
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(2.w),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.white.withValues(alpha: 0.2),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        isClockIn
                                            ? Icons.access_time
                                            : Icons.timer_off,
                                        size: 6.w,
                                        color: Colors.white,
                                      ),
                                    ),
                                    SizedBox(width: 3.w),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          isClockIn ? 'Clock In' : 'Clock Out',
                                          style: GoogleFonts.poppins(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            letterSpacing: 1,
                                          ),
                                        ),
                                        Text(
                                          isClockIn
                                              ? 'Start Your Work Day'
                                              : 'Complete Your Shift',
                                          style: GoogleFonts.poppins(
                                            fontSize: 11.sp,
                                            color: Colors.white
                                                .withValues(alpha: 0.8),
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),

                                // Right - Current time
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      DateFormat('HH:mm:ss').format(now),
                                      style: GoogleFonts.poppins(
                                        fontSize: 14.sp,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 1.5,
                                      ),
                                    ),
                                    Text(
                                      DateFormat('MMM dd, yyyy').format(now),
                                      style: GoogleFonts.poppins(
                                        fontSize: 10.sp,
                                        color:
                                            Colors.white.withValues(alpha: 0.9),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStaffGrid() {
    return BlocBuilder<StaffBloc, StaffState>(
      builder: (context, staffState) {
        final filteredStaffList = widget.action == ClockAction.clockOut
            ? staffState.clockedInStaff
            : staffState.allStaff;

        if (staffState.status == StaffStatus.loading) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.action == ClockAction.clockIn
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 3.w,
                vertical:
                    MediaQuery.of(context).orientation == Orientation.landscape
                        ? 1.h
                        : 1.5.h,
              ),
              child: Text(
                widget.action == ClockAction.clockOut
                    ? 'Select Staff to Clock Out'
                    : 'Select Staff to Clock In',
                style: GoogleFonts.poppins(
                  fontSize: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? 12.sp
                      : 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? 4
                          : 4,
                  childAspectRatio:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? 1.2
                          : 1.2,
                  crossAxisSpacing: 1.w,
                  mainAxisSpacing: 1.h,
                ),
                itemCount: filteredStaffList.length,
                itemBuilder: (context, index) {
                  final staff = filteredStaffList[index];
                  final isSelected = _selectedStaff?.id == staff.id;
                  final isClockIn = widget.action == ClockAction.clockIn;

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isClockIn
                                  ? [
                                      Colors.green.shade400,
                                      Colors.green.shade600
                                    ]
                                  : [
                                      Colors.orange.shade400,
                                      Colors.orange.shade600
                                    ],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.white24,
                                Colors.white24,
                              ],
                            ),
                      borderRadius: BorderRadius.circular(3.w),
                      border: Border.all(
                        color: isSelected
                            ? Colors.white.withValues(alpha: 0.5)
                            : Colors.grey.withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color:
                                    (isClockIn ? Colors.green : Colors.orange)
                                        .withValues(alpha: 0.4),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ]
                          : [],
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(3.w),
                      onTap: () {
                        setState(() {
                          _selectedStaff = staff;
                          _pin = '';
                          _showError = false;
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.all(1.5.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircleAvatar(
                              radius: MediaQuery.of(context).orientation ==
                                      Orientation.landscape
                                  ? 2.w
                                  : 4.w,
                              backgroundColor: isSelected
                                  ? Colors.white.withValues(alpha: 0.2)
                                  : Colors.grey.withValues(alpha: 0.3),
                              child: Icon(
                                Icons.person,
                                size: MediaQuery.of(context).orientation ==
                                        Orientation.landscape
                                    ? 3.w
                                    : 4.w,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(
                                height: MediaQuery.of(context).orientation ==
                                        Orientation.landscape
                                    ? 1.h
                                    : 0.6.h),
                            Flexible(
                              child: Text(
                                staff.name,
                                style: GoogleFonts.poppins(
                                  fontSize:
                                      MediaQuery.of(context).orientation ==
                                              Orientation.landscape
                                          ? 10.sp
                                          : 10.sp,
                                  fontWeight: isSelected
                                      ? FontWeight.bold
                                      : FontWeight.w500,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (MediaQuery.of(context).orientation ==
                                Orientation.portrait)
                              SizedBox(height: 0.2.h),
                            Flexible(
                              child: Text(
                                staff.role ?? 'Staff',
                                style: GoogleFonts.poppins(
                                  fontSize:
                                      MediaQuery.of(context).orientation ==
                                              Orientation.landscape
                                          ? 10.sp
                                          : 8.sp,
                                  color: Colors.white.withValues(alpha: 0.8),
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (widget.action == ClockAction.clockOut &&
                                MediaQuery.of(context).orientation ==
                                    Orientation.portrait)
                              Container(
                                margin: EdgeInsets.only(top: 0.2.h),
                                padding: EdgeInsets.symmetric(
                                  horizontal: 1.w,
                                  vertical: 0.2.h,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(0.5.w),
                                ),
                                child: Text(
                                  'Active',
                                  style: GoogleFonts.poppins(
                                    fontSize: 6.sp,
                                    color: Colors.green.shade300,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPinEntrySection() {
    final isClockIn = widget.action == ClockAction.clockIn;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            padding: EdgeInsets.all(isLandscape ? 2.w : 3.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Status indicator
                Container(
                  padding: EdgeInsets.all(isLandscape ? 1.w : 3.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isClockIn
                          ? [Colors.green.shade100, Colors.green.shade50]
                          : [Colors.orange.shade100, Colors.orange.shade50],
                    ),
                    borderRadius:
                        BorderRadius.circular(isLandscape ? 1.w : 3.w),
                    border: Border.all(
                      color: isClockIn
                          ? Colors.green.shade300
                          : Colors.orange.shade300,
                      width: 2,
                    ),
                  ),
                  child: isLandscape
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              isClockIn
                                  ? Icons.schedule
                                  : Icons.schedule_outlined,
                              size: 4.w,
                              color: isClockIn
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700,
                            ),
                            SizedBox(width: 2.w),
                            Flexible(
                              child: Text(
                                _selectedStaff == null
                                    ? 'Select Staff Member Above'
                                    : '${isClockIn ? 'Clock In' : 'Clock Out'} - ${_selectedStaff!.name}',
                                style: GoogleFonts.poppins(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                  color: isClockIn
                                      ? Colors.green.shade800
                                      : Colors.orange.shade800,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            if (_selectedStaff != null) ...[
                              SizedBox(width: 2.w),
                              Text(
                                '• Enter PIN',
                                style: GoogleFonts.poppins(
                                  fontSize: 12.sp,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ],
                        )
                      : Column(
                          children: [
                            Icon(
                              isClockIn
                                  ? Icons.schedule
                                  : Icons.schedule_outlined,
                              size: 10.w,
                              color: isClockIn
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700,
                            ),
                            SizedBox(height: 1.5.h),
                            Text(
                              _selectedStaff == null
                                  ? 'Select Staff Member Above'
                                  : '${isClockIn ? 'Clock In' : 'Clock Out'} - ${_selectedStaff!.name}',
                              style: GoogleFonts.poppins(
                                fontSize: 5.sp,
                                fontWeight: FontWeight.bold,
                                color: isClockIn
                                    ? Colors.green.shade800
                                    : Colors.orange.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            if (_selectedStaff != null) ...[
                              SizedBox(height: 0.8.h),
                              Text(
                                'Enter your 5-digit PIN',
                                style: GoogleFonts.poppins(
                                  fontSize: 4.sp,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ],
                        ),
                ),

                SizedBox(height: isLandscape ? 2.h : 3.h),

                // PIN dots
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    5,
                    (index) => Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: isLandscape ? 0.5.w : 1.w),
                      width: isLandscape ? 2.5.w : 4.w,
                      height: isLandscape ? 2.5.w : 4.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index < _pin.length
                            ? (isClockIn ? Colors.green : Colors.orange)
                            : Colors.grey.shade300,
                        border: Border.all(
                          color: index < _pin.length
                              ? (isClockIn
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700)
                              : Colors.grey.shade400,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ),

                if (_showError) ...[
                  SizedBox(height: 1.h),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(2.w),
                      border: Border.all(color: Colors.red.shade300),
                    ),
                    child: Text(
                      'Invalid PIN. Please try again.',
                      style: GoogleFonts.poppins(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ],

                SizedBox(height: isLandscape ? 2.h : 3.h),

                // Keypad
                Container(
                  padding:EdgeInsets.symmetric(horizontal:4.w),

                  child: GridView.count(
                    shrinkWrap: true,
                    crossAxisCount: 3,
                    childAspectRatio: isLandscape ? 1.5: 1,
                    mainAxisSpacing: isLandscape ? 1.h : 1.5.h,
                    crossAxisSpacing: isLandscape ? 1.w : 3.w,
                    children: [
                      for (var i = 1; i <= 9; i++)
                        _buildKeypadButton(i.toString()),
                      _buildKeypadButton(''),
                      _buildKeypadButton('0'),
                      _buildKeypadButton('⌫'),
                    ],
                  ),
                ),

                SizedBox(height: isLandscape ? 1.h : 3.h),

                // Cancel button
                TextButton.icon(
                  onPressed: () => context.pop(),
                  icon: Icon(
                    Icons.arrow_back,
                    color: Colors.red,
                    size: isLandscape ? 2.w : 5.w,
                  ),
                  label: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      color: Colors.red,
                      fontSize: isLandscape ? 12.sp : 13.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildKeypadButton(String value) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      margin: EdgeInsets.all(0.25.w),
      decoration: BoxDecoration(
        color: Colors.white24,
        borderRadius: BorderRadius.circular(isLandscape ? 1.w : 3.w),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(isLandscape ? 1.w : 3.w),
        onTap: _selectedStaff == null || value.isEmpty
            ? null
            : () {
                if (value == '⌫') {
                  _onBackspace();
                } else {
                  _onKeyPress(value);
                }
              },
        child: Container(
          padding:EdgeInsets.zero,
          alignment: Alignment.center,
          child: value == '⌫'
              ? Icon(
                  Icons.backspace_outlined,
                  color: Colors.grey.shade600,
                  size: isLandscape ? 4.w : 5.w,
                )
              : Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 16.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.normal,
                  ),
                ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AttendanceBloc, AttendanceState>(
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status == AttendanceStatus.success) {
          // Pop the current page and return to pin entry
          context.pop();

          // Show a brief success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${_selectedStaff?.name} has ${widget.action == ClockAction.clockIn ? 'clocked in' : 'clocked out'} successfully',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (state.status == AttendanceStatus.error) {
          setState(() {
            _pin = '';
            _showError = true;
          });

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.error ??
                    'Failed to ${widget.action == ClockAction.clockIn ? 'clock in' : 'clock out'}. Please try again.',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: BlocBuilder<AttendanceBloc, AttendanceState>(
        builder: (context, state) {
          return Sizer(
            builder: (context, orientation, deviceType) {
              return Scaffold(
                backgroundColor: const Color(0xFF1A1A2E),
                body: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF1A1A2E),
                        const Color(0xFF16213E),
                        const Color(0xFF0F3460),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      SafeArea(
                        child: Column(
                          children: [
                            // Header with time tracking info
                            Padding(
                              padding: EdgeInsets.all(2.w),
                              child: _buildTimeTrackingHeader(),
                            ),

                            // Main content area
                            Expanded(
                              child: orientation == Orientation.portrait
                                  ? Column(
                                      children: [
                                        // Staff selection (top half)
                                        Expanded(
                                          flex: 3,
                                          child: _buildStaffGrid(),
                                        ),

                                        // PIN entry (bottom half)
                                        Expanded(
                                          flex: 2,
                                          child: SingleChildScrollView(
                                            child: _buildPinEntrySection(),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Row(
                                      children: [
                                        // Staff selection (left side)
                                        Expanded(
                                          flex: 3,
                                          child: _buildStaffGrid(),
                                        ),

                                        // PIN entry (right side)
                                        Expanded(
                                          flex: 2,
                                          child: SingleChildScrollView(
                                            child: _buildPinEntrySection(),
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ],
                        ),
                      ),

                      // Loading overlay
                      if (state.status == AttendanceStatus.loading)
                        Positioned.fill(
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.7),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.all(6.w),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: widget.action == ClockAction.clockIn
                                        ? [
                                            Colors.green.shade400,
                                            Colors.green.shade600
                                          ]
                                        : [
                                            Colors.orange.shade400,
                                            Colors.orange.shade600
                                          ],
                                  ),
                                  borderRadius: BorderRadius.circular(4.w),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          (widget.action == ClockAction.clockIn
                                                  ? Colors.green
                                                  : Colors.orange)
                                              .withValues(alpha: 0.3),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(
                                      valueColor:
                                          const AlwaysStoppedAnimation<Color>(
                                              Colors.white),
                                      strokeWidth: 3,
                                    ),
                                    SizedBox(height: 3.h),
                                    Text(
                                      widget.action == ClockAction.clockIn
                                          ? 'Clocking In...'
                                          : 'Clocking Out...',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 1.h),
                                    Text(
                                      'Please wait',
                                      style: GoogleFonts.poppins(
                                        color:
                                            Colors.white.withValues(alpha: 0.8),
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
