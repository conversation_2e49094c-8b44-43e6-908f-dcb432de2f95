import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../router/router_constants.dart';

class NavigationService {
  static const MethodChannel _channel =
      MethodChannel('com.zynktech.easydine_main/navigation');

  /// Handle back button press - navigate to home instead of closing app
  static Future<bool> handleBackButton(BuildContext context) async {
    try {
      final router = GoRouter.of(context);
      final currentLocation =
          router.routerDelegate.currentConfiguration.fullPath;

      print('🔙 Back button pressed on: $currentLocation');

      // Special handling for certain pages that should not allow back navigation
      final restrictedPages = ['/daily-checklist', '/pin-entry', '/login'];
      if (restrictedPages.contains(currentLocation)) {
        print('🚫 Back navigation restricted on: $currentLocation');
        return true; // Handled - prevent back navigation
      }

      // Always navigate to home page on back button, do not minimize app
      if (currentLocation != RouterConstants.home && currentLocation != '/home') {
        print('🏠 Navigating to home page');
        router.go(RouterConstants.home);
        return true; // Handled - don't close the app
      }

      // If already on home, do nothing (prevent app close)
      print('🏠 Already on home page, back press ignored');
      return true; // Handled - don't close the app
    } catch (e) {
      print('❌ Error handling back button: $e');
      // On error, just prevent app from closing
      return true;
    }
  }
}
