import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/order_model.dart';
import '../utils/http_client.dart';
import '../config/env_config.dart';

/// Service for handling order-related API calls
class OrderService {
  static String get _baseUrl => EnvConfig.apiBaseUrl;

  /// Fetch all orders from the API
  /// GET {{LAMBDA_HOST}}/order/view_all
  static Future<OrdersApiResponse?> getAllOrders({
    int page = 1,
    int limit = 10,
    String? status,
    String? orderType,
    String? branchId,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status;
      if (orderType != null) queryParams['orderType'] = orderType;
      if (branchId != null) queryParams['branchId'] = branchId;

      final url = '$_baseUrl/order/view_all';
      debugPrint('🍽️ OrderService: Fetching orders from $url');
      debugPrint('🍽️ OrderService: Query params: $queryParams');

      final response = await HttpClientService.get(url, params: queryParams);
      debugPrint('🍽️ OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🍽️ OrderService: Successfully parsed orders response');

        return OrdersApiResponse.fromJson(jsonData);
      } else {
        debugPrint('🍽️ OrderService: HTTP error ${response.statusCode}');
        debugPrint('🍽️ OrderService: Response body: ${response.body}');
        throw Exception('Failed to fetch orders: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error fetching orders: $e');
      return null;
    }
  }

  /// Fetch orders by status
  static Future<OrdersApiResponse?> getOrdersByStatus(String status) async {
    return getAllOrders(status: status);
  }

  /// Fetch orders by order type
  static Future<OrdersApiResponse?> getOrdersByType(String orderType) async {
    return getAllOrders(orderType: orderType);
  }

  /// Fetch orders for a specific branch
  static Future<OrdersApiResponse?> getOrdersByBranch(String branchId) async {
    return getAllOrders(branchId: branchId);
  }

  /// Update order status
  /// PATCH {{LAMBDA_HOST}}/order/update-status/:orderDetailId
  static Future<bool> updateOrderStatus(
      String orderDetailId, String status) async {
    try {
      final url = '$_baseUrl/order/update-status/$orderDetailId';
      debugPrint('🍽️ OrderService: Updating order status at $url');

      final body = json.encode({'status': status});
      final response = await HttpClientService.patch(url, body: body);

      debugPrint(
          '🍽️ OrderService: Update status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully updated order status');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to update order status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error updating order status: $e');
      return false;
    }
  }

  /// Update order details (partial update)
  /// PATCH {{LAMBDA_HOST}}/order/update/:orderDetailId
  static Future<bool> updateOrderDetails(
      String orderDetailId, Map<String, dynamic> updateData) async {
    try {
      final url = '$_baseUrl/order/update/$orderDetailId';
      debugPrint('🍽️ OrderService: Updating order details at $url');
      debugPrint('🍽️ OrderService: Update data: ${json.encode(updateData)}');

      final response =
          await HttpClientService.patch(url, body: json.encode(updateData));

      debugPrint(
          '🍽️ OrderService: Update order response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully updated order details');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to update order details: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error updating order details: $e');
      return false;
    }
  }

  /// Get order details by ID
  /// GET {{LAMBDA_HOST}}/order/get-order/:orderDetailId
  static Future<OrderDetail?> getOrderById(String orderDetailId) async {
    try {
      final url = '$_baseUrl/order/get-order/$orderDetailId';
      debugPrint('🍽️ OrderService: Fetching order details from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🍽️ OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🍽️ OrderService: Successfully parsed order details');

        return OrderDetail.fromJson(jsonData['data']);
      } else {
        debugPrint('🍽️ OrderService: HTTP error ${response.statusCode}');
        throw Exception(
            'Failed to fetch order details: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error fetching order details: $e');
      return null;
    }
  }

  /// Convert OrderDetail to legacy format for backward compatibility
  static Map<String, dynamic> convertToLegacyFormat(OrderDetail orderDetail) {
    return {
      'id': orderDetail.orderCode,
      'table': orderDetail.table?.name ??
          'Unknown Table', // Convert TableInfo to string
      'status': _mapStatusToLegacy(orderDetail.status),
      'type': orderDetail.orderType.name,
      'items': orderDetail.orderItems
          .map((item) => {
                'id': item.orderItemId,
                'name': item.name,
                'quantity': item.quantity,
                'price': double.tryParse(item.price) ?? 0.0,
              })
          .toList(),
      'time': _formatTime(orderDetail.createdAt),
      'customer': orderDetail.customerInfo?.name ??
          orderDetail.orderedBy?.name ??
          'Unknown',
      'total': double.tryParse(orderDetail.total) ?? 0.0,
      'orderDetailId': orderDetail.orderDetailId,
      'orderApproval': orderDetail.orderApproval,
      'estimatedPrepTime': orderDetail.estimatedPrepTime,
      'assignedWaiter': orderDetail.assignedWaiter?.name,
      'assignedChef': orderDetail.assignedChef?.name,
      'notes': orderDetail.notes,
      'numberOfPeople': orderDetail.numberOfPeople,
      'customerInfo': orderDetail.customerInfo != null
          ? {
              'name': orderDetail.customerInfo!.name,
              'phone': orderDetail.customerInfo!.phoneNumber,
              'email': orderDetail.customerInfo!.email,
            }
          : null,
      'orderedBy': orderDetail.orderedBy != null
          ? {
              'name': orderDetail.orderedBy!.name,
              'id': orderDetail.orderedBy!.id,
            }
          : null,
      'prepStartTime': orderDetail.prepStartTime != null
          ? _formatTime(orderDetail.prepStartTime!)
          : null,
      'readyTime': orderDetail.readyTime != null
          ? _formatTime(orderDetail.readyTime!)
          : null,
      'servedTime': orderDetail.servedTime != null
          ? _formatTime(orderDetail.servedTime!)
          : null,
      'bills': orderDetail.bills
          .map((bill) => {
                'billId': bill.billId,
                'billNumber': bill.billNumber,
                'subtotal': bill.subtotal,
                'tipAmount': bill.tipAmount,
                'totalTax': bill.totalTax,
                'totalDiscount': bill.totalDiscount,
                'serviceCharge': bill.serviceCharge,
                'deliveryCharge': bill.deliveryCharge,
                'packagingCharge': bill.packagingCharge,
                'totalAmount': bill.totalAmount,
                'status': bill.status,
                'taxBreakdown': bill.taxBreakdown,
                'discounts': bill.discounts,
                'payments': bill.payments,
                'customerInfo': bill.customerInfo != null
                    ? {
                        'name': bill.customerInfo!.name,
                        'phone': bill.customerInfo!.phoneNumber,
                        'email': bill.customerInfo!.email,
                      }
                    : null,
                'branchId': bill.branchId,
                'notes': bill.notes,
                'createdAt': bill.createdAt.toIso8601String(),
                'updatedAt': bill.updatedAt.toIso8601String(),
              })
          .toList(),
    };
  }

  /// Map API status to legacy status format
  static String _mapStatusToLegacy(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'IN_PREPARATION':
        return 'Cooking';
      case 'READY':
        return 'Ready';
      case 'SERVED':
        return 'Served';
      case 'COMPLETED':
        return 'Completed';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return apiStatus;
    }
  }

  /// Cancel/Delete order
  /// DELETE {{LAMBDA_HOST}}/order/delete/:orderDetailId
  static Future<bool> cancelOrder(String orderDetailId) async {
    try {
      final url = '$_baseUrl/order/delete/$orderDetailId';
      debugPrint('🍽️ OrderService: Canceling order at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🍽️ OrderService: Cancel order response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully canceled order');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to cancel order: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error canceling order: $e');
      return false;
    }
  }

  /// Format DateTime to time string
  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }
}
