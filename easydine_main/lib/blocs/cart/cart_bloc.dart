import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../services/cart_service.dart';
import '../../services/order_service.dart';
import '../../models/cart_models.dart';
import 'cart_event.dart';
import 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  CartBloc() : super(const CartInitial()) {
    on<LoadCart>(_onLoadCart);
    on<RefreshCart>(_onRefreshCart);
    on<AddItemToCart>(_onAddItemToCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<UpdateCartItem>(_onUpdateCartItem);
    on<RemoveCartItem>(_onRemoveCartItem);
    on<ClearCart>(_onClearCart);
    on<HoldCart>(_onHoldCart);
    on<ActivateCart>(_onActivateCart);
    on<ConfirmCart>(_onConfirmCart);
    on<DeleteCart>(_onDeleteCart);
    on<FetchAllCarts>(_onFetchAllCarts);
    on<SetCartError>(_onSetCartError);
    on<ClearCartError>(_onClearCartError);
    on<LoadOrderIntoCart>(_onLoadOrderIntoCart);
    on<UpdateOriginalOrder>(_onUpdateOriginalOrder);
  }

  /// Load or create cart for current staff member
  Future<void> _onLoadCart(LoadCart event, Emitter<CartState> emit) async {
    emit(const CartLoading());

    try {
      debugPrint('🛒 CartBloc: Loading cart...');
      final cart = await CartService.getOrCreateCart();

      if (cart != null) {
        debugPrint('🛒 CartBloc: Cart loaded successfully');
        emit(CartLoaded(cart: cart));
      } else {
        debugPrint('❌ CartBloc: Failed to load cart');
        emit(const CartError(error: 'Failed to load cart'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error loading cart: $e');
      emit(CartError(error: 'Error loading cart: $e'));
    }
  }

  /// Refresh cart data
  Future<void> _onRefreshCart(
      RefreshCart event, Emitter<CartState> emit) async {
    if (state.currentCart != null) {
      emit(CartProcessing(
          currentCart: state.currentCart, allCarts: state.allCarts));
    }

    try {
      final cart = await CartService.getOrCreateCart();

      if (cart != null) {
        emit(CartLoaded(
          cart: cart,
          allCarts: state.allCarts,
          originalOrderId: state.originalOrderId,
        ));
      } else {
        emit(CartError(error: 'Failed to refresh cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error refreshing cart: $e'));
    }
  }

  /// Add item to cart
  Future<void> _onAddItemToCart(
      AddItemToCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      debugPrint('🛒 CartBloc: Adding item to cart...');
      final success = await CartService.addItemToCart(event.request);

      if (success) {
        debugPrint('🛒 CartBloc: Item added successfully, refreshing cart...');
        // Refresh cart to get updated data
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));

          // If this cart was loaded from an order, update the original order
          if (state.originalOrderId != null) {
            add(const UpdateOriginalOrder());
          }
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after adding item'));
        }
      } else {
        debugPrint('❌ CartBloc: Failed to add item to cart');
        emit(CartError(error: 'Failed to add item to cart'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error adding item to cart: $e');
      emit(CartError(error: 'Error adding item to cart: $e'));
    }
  }

  /// Update item quantity
  Future<void> _onUpdateCartItemQuantity(
      UpdateCartItemQuantity event, Emitter<CartState> emit) async {
    debugPrint(
        '🛒 CartBloc: Starting quantity update for item ${event.cartItemId} to quantity ${event.quantity}');

    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      debugPrint('🛒 CartBloc: Calling CartService.updateItemQuantity...');
      final success = await CartService.updateItemQuantity(
          event.cartItemId, event.quantity);

      if (success) {
        debugPrint(
            '🛒 CartBloc: Quantity update successful, refreshing cart...');
        // Refresh cart to get updated data
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          debugPrint(
              '🛒 CartBloc: Cart refreshed successfully. Items count: ${updatedCart.items.length}');
          // Log the updated quantities for debugging
          for (final item in updatedCart.items) {
            debugPrint(
                '🛒 CartBloc: Item ${item.id} has quantity ${item.quantity}');
          }
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));

          // If this cart was loaded from an order, update the original order
          if (state.originalOrderId != null) {
            add(const UpdateOriginalOrder());
          }
        } else {
          debugPrint(
              '❌ CartBloc: Failed to refresh cart after updating quantity');
          emit(const CartError(
              error: 'Failed to refresh cart after updating quantity'));
        }
      } else {
        debugPrint('❌ CartBloc: Failed to update item quantity');
        emit(const CartError(error: 'Failed to update item quantity'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error updating item quantity: $e');
      emit(CartError(error: 'Error updating item quantity: $e'));
    }
  }

  /// Update item details
  Future<void> _onUpdateCartItem(
      UpdateCartItem event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success =
          await CartService.updateItem(event.cartItemId, event.updates);

      if (success) {
        // Refresh cart to get updated data
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));

          // If this cart was loaded from an order, update the original order
          if (state.originalOrderId != null) {
            add(const UpdateOriginalOrder());
          }
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after updating item'));
        }
      } else {
        emit(const CartError(error: 'Failed to update item'));
      }
    } catch (e) {
      emit(CartError(error: 'Error updating item: $e'));
    }
  }

  /// Remove item from cart
  Future<void> _onRemoveCartItem(
      RemoveCartItem event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.deleteItemFromCart(event.cartItemId);

      if (success) {
        // Refresh cart to get updated data
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));

          // If this cart was loaded from an order, update the original order
          if (state.originalOrderId != null) {
            add(const UpdateOriginalOrder());
          }
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after removing item'));
        }
      } else {
        emit(const CartError(error: 'Failed to remove item from cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error removing item from cart: $e'));
    }
  }

  /// Clear all items from cart
  Future<void> _onClearCart(ClearCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.clearCart();

      if (success) {
        // Refresh cart to get updated data
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(error: 'Failed to refresh cart after clearing'));
        }
      } else {
        emit(const CartError(error: 'Failed to clear cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error clearing cart: $e'));
    }
  }

  /// Set cart to hold status
  Future<void> _onHoldCart(HoldCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.holdCart();

      if (success) {
        // Refresh cart to get updated status
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(error: 'Failed to refresh cart after holding'));
        }
      } else {
        emit(const CartError(error: 'Failed to hold cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error holding cart: $e'));
    }
  }

  /// Activate cart
  Future<void> _onActivateCart(
      ActivateCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.activateCart(event.cartId);

      if (success) {
        // Refresh cart to get updated status
        final updatedCart = await CartService.getOrCreateCart();
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after activation'));
        }
      } else {
        emit(const CartError(error: 'Failed to activate cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error activating cart: $e'));
    }
  }

  /// Confirm cart (place order)
  Future<void> _onConfirmCart(
      ConfirmCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.confirmCart(event.request);

      if (success) {
        // After confirming, load a new cart
        final newCart = await CartService.getOrCreateCart();
        if (newCart != null) {
          emit(CartLoaded(cart: newCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to create new cart after confirmation'));
        }
      } else {
        emit(const CartError(error: 'Failed to confirm cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error confirming cart: $e'));
    }
  }

  /// Delete cart
  Future<void> _onDeleteCart(DeleteCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.deleteCart(event.cartId);

      if (success) {
        // Create a new cart after deletion
        final newCart = await CartService.getOrCreateCart();
        if (newCart != null) {
          emit(CartLoaded(cart: newCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to create new cart after deletion'));
        }
      } else {
        emit(const CartError(error: 'Failed to delete cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error deleting cart: $e'));
    }
  }

  /// Fetch all carts
  Future<void> _onFetchAllCarts(
      FetchAllCarts event, Emitter<CartState> emit) async {
    try {
      final carts = await CartService.fetchAllCarts();

      if (carts != null) {
        emit(state.copyWith(allCarts: carts));
      } else {
        emit(state.copyWith(error: 'Failed to fetch all carts'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error fetching all carts: $e'));
    }
  }

  /// Set error message
  void _onSetCartError(SetCartError event, Emitter<CartState> emit) {
    emit(state.copyWith(error: event.error));
  }

  /// Clear error message
  void _onClearCartError(ClearCartError event, Emitter<CartState> emit) {
    emit(state.copyWith(clearError: true));
  }

  /// Load order data into cart for modification
  Future<void> _onLoadOrderIntoCart(
      LoadOrderIntoCart event, Emitter<CartState> emit) async {
    emit(const CartLoading());

    try {
      debugPrint('🛒 CartBloc: Loading order data into cart...');

      // First clear the current cart
      await CartService.clearCart();

      // Get the order data
      final orderData = event.orderData;
      final orderType = orderData['type'] as String;
      final items = orderData['items'] as List<dynamic>;
      final tableNumber = orderData['table'] as String?;
      final customerInfo = orderData['customer'] as String?;

      debugPrint('🛒 CartBloc: Order type: $orderType');
      debugPrint('🛒 CartBloc: Loading ${items.length} items into cart');

      // Filter and prepare order data based on order type
      String notes = 'Loaded from running order ($orderType)';

      // Add order type specific information to notes
      switch (orderType.toLowerCase()) {
        case 'dine-in':
        case 'dine in':
          if (tableNumber != null) {
            notes += ' - Table: $tableNumber';
          }
          break;
        case 'takeaway':
          notes += ' - Takeaway order';
          if (customerInfo != null) {
            notes += ' - Customer: $customerInfo';
          }
          break;
        case 'delivery':
          notes += ' - Delivery order';
          if (customerInfo != null) {
            notes += ' - Customer: $customerInfo';
          }
          break;
      }

      // Add each item to the cart
      for (final item in items) {
        final request = AddItemToCartRequest(
          quantity: item['quantity'] ?? 1,
          dishId: item['id'] ?? '',
          type: 'customized',
          notes: notes,
        );

        await CartService.addItemToCart(request);
      }

      // Load the updated cart
      final cart = await CartService.getOrCreateCart();

      if (cart != null) {
        debugPrint('🛒 CartBloc: Order data loaded successfully into cart');
        debugPrint('🛒 CartBloc: Cart populated with order type: $orderType');
        debugPrint('🛒 CartBloc: Original order ID: ${event.originalOrderId}');
        emit(CartLoaded(cart: cart, originalOrderId: event.originalOrderId));
      } else {
        debugPrint('❌ CartBloc: Failed to load cart after adding order items');
        emit(const CartError(
            error: 'Failed to load cart after adding order items'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error loading order into cart: $e');
      emit(CartError(error: 'Error loading order into cart: $e'));
    }
  }

  /// Update original order with current cart items
  Future<void> _onUpdateOriginalOrder(
      UpdateOriginalOrder event, Emitter<CartState> emit) async {
    try {
      final currentState = state;

      // Only proceed if we have an original order ID and current cart
      if (currentState.originalOrderId == null ||
          currentState.currentCart == null) {
        debugPrint('🛒 CartBloc: No original order ID or cart to update');
        return;
      }

      debugPrint(
          '🛒 CartBloc: Updating original order ${currentState.originalOrderId}');

      // Convert current cart items to order items format
      final orderItems = currentState.currentCart!.items
          .map((cartItem) => {
                'orderItemId': cartItem.id, // Use cart item ID as order item ID
                'dishId': cartItem.dishId,
                'name': cartItem.name,
                'quantity': cartItem.quantity,
                'price': cartItem.price,
                'notes': cartItem.notes,
                // Include customizations if available
                if (cartItem.dishAddons.isNotEmpty)
                  'dishAddons': cartItem.dishAddons
                      .map((addon) => {
                            'id': addon.id,
                            'quantity': addon.quantity,
                          })
                      .toList(),
                if (cartItem.dishExtras.isNotEmpty)
                  'dishExtras': cartItem.dishExtras
                      .map((extra) => {
                            'id': extra.id,
                            'quantity': extra.quantity,
                          })
                      .toList(),
                if (cartItem.dishSides.isNotEmpty)
                  'dishSides': cartItem.dishSides
                      .map((side) => {
                            'id': side.id,
                            'quantity': side.quantity,
                          })
                      .toList(),
                if (cartItem.dishBeverages.isNotEmpty)
                  'dishBeverages': cartItem.dishBeverages
                      .map((beverage) => {
                            'id': beverage.id,
                            'quantity': beverage.quantity,
                          })
                      .toList(),
                if (cartItem.dishDesserts.isNotEmpty)
                  'dishDesserts': cartItem.dishDesserts
                      .map((dessert) => {
                            'id': dessert.id,
                            'quantity': dessert.quantity,
                          })
                      .toList(),
                if (cartItem.allergyIds.isNotEmpty)
                  'allergyIds': cartItem.allergyIds,
              })
          .toList();

      // Prepare update data
      final updateData = {
        'orderItems': orderItems,
        'total': double.tryParse(currentState.currentCart!.total) ?? 0.0,
        'notes': currentState.currentCart!.notes ?? 'Updated from cart',
      };

      // Send update to backend
      final success = await OrderService.updateOrderDetails(
        currentState.originalOrderId!,
        updateData,
      );

      if (success) {
        debugPrint('🛒 CartBloc: Successfully updated original order');
      } else {
        debugPrint('❌ CartBloc: Failed to update original order');
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error updating original order: $e');
    }
  }
}
