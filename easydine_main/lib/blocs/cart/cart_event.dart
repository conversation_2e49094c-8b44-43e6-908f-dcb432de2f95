import 'package:equatable/equatable.dart';
import '../../models/cart_models.dart';

abstract class CartEvent extends Equatable {
  const CartEvent();

  @override
  List<Object?> get props => [];
}

/// Load or create cart for the current staff member
class LoadCart extends CartEvent {
  const LoadCart();
}

/// Refresh cart data from server
class Refresh<PERSON>art extends CartEvent {
  const RefreshCart();
}

/// Add item to cart
class AddItemToCart extends CartEvent {
  final AddItemToCartRequest request;

  const AddItemToCart({required this.request});

  @override
  List<Object> get props => [request];
}

/// Update item quantity in cart
class UpdateCartItemQuantity extends CartEvent {
  final String cartItemId; // Changed from dishId to cartItemId
  final int quantity;

  const UpdateCartItemQuantity({
    required this.cartItemId,
    required this.quantity,
  });

  @override
  List<Object> get props => [cartItemId, quantity];
}

/// Update item details in cart
class UpdateCartItem extends CartEvent {
  final String cartItemId; // Changed from dishId to cartItemId
  final Map<String, dynamic> updates;

  const UpdateCartItem({
    required this.cartItemId,
    required this.updates,
  });

  @override
  List<Object> get props => [cartItemId, updates];
}

/// Remove item from cart
class RemoveCartItem extends CartEvent {
  final String cartItemId; // Changed from dishId to cartItemId

  const RemoveCartItem({required this.cartItemId});

  @override
  List<Object> get props => [cartItemId];
}

/// Clear all items from cart
class ClearCart extends CartEvent {
  const ClearCart();
}

/// Set cart to hold status
class HoldCart extends CartEvent {
  const HoldCart();
}

/// Activate cart (set status to active)
class ActivateCart extends CartEvent {
  final String cartId;

  const ActivateCart({required this.cartId});

  @override
  List<Object> get props => [cartId];
}

/// Confirm cart (place order)
class ConfirmCart extends CartEvent {
  final ConfirmCartRequest request;

  const ConfirmCart({required this.request});

  @override
  List<Object> get props => [request];
}

/// Delete cart
class DeleteCart extends CartEvent {
  final String cartId;

  const DeleteCart({required this.cartId});

  @override
  List<Object> get props => [cartId];
}

/// Fetch all carts
class FetchAllCarts extends CartEvent {
  const FetchAllCarts();
}

/// Set error message
class SetCartError extends CartEvent {
  final String? error;

  const SetCartError({this.error});

  @override
  List<Object?> get props => [error];
}

/// Clear error message
class ClearCartError extends CartEvent {
  const ClearCartError();
}

/// Load order data into cart for modification
class LoadOrderIntoCart extends CartEvent {
  final Map<String, dynamic> orderData;
  final String? originalOrderId; // Track the original order ID for updates

  const LoadOrderIntoCart({
    required this.orderData,
    this.originalOrderId,
  });

  @override
  List<Object?> get props => [orderData, originalOrderId];
}

/// Update original order with current cart items
class UpdateOriginalOrder extends CartEvent {
  const UpdateOriginalOrder();

  @override
  List<Object> get props => [];
}
