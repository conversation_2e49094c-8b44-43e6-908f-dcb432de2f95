import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'running_orders_event.dart';
import 'running_orders_state.dart';
import '../../services/order_service.dart';

class RunningOrdersBloc extends Bloc<RunningOrdersEvent, RunningOrdersState> {
  RunningOrdersBloc() : super(const RunningOrdersState()) {
    on<FetchRunningOrders>(_onFetchRunningOrders);
    on<FilterByCategory>(_onFilterByCategory);
    on<FilterByStatus>(_onFilterByStatus);
    on<SortOrders>(_onSortOrders);
    on<RefreshOrders>(_onRefreshOrders);
    on<UpdateOrderStatus>(_onUpdateOrderStatus);
  }

  // Sample data removed - use empty array instead
  final List<Map<String, dynamic>> _sampleOrders = [];

  Future<void> _onFetchRunningOrders(
    FetchRunningOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    emit(state.copyWith(status: RunningOrdersStatus.loading));

    try {
      debugPrint('🍽️ RunningOrdersBloc: Fetching orders from API...');

      // Fetch orders from the API
      final ordersResponse = await OrderService.getAllOrders(
        limit: 50, // Get more orders for better display
      );

      if (ordersResponse != null && ordersResponse.success) {
        debugPrint(
            '🍽️ RunningOrdersBloc: Successfully fetched ${ordersResponse.data.data.length} orders');

        // Convert API response to legacy format for compatibility
        final legacyOrders = ordersResponse.data.data.map((orderDetail) {
          final legacyOrder = OrderService.convertToLegacyFormat(orderDetail);
          // Store the original OrderDetail for printing purposes
          legacyOrder['_originalOrderDetail'] = orderDetail;
          return legacyOrder;
        }).toList();

        // Filter out completed and cancelled orders for running orders page
        final activeOrders = legacyOrders.where((order) {
          final status = (order['status'] ?? '').toString().toUpperCase();
          return status != 'COMPLETED' && status != 'CANCELLED';
        }).toList();

        emit(state.copyWith(
          orders: activeOrders,
          allOrders: activeOrders, // Store filtered active orders
          status: RunningOrdersStatus.success,
        ));
      } else {
        debugPrint(
            '🍽️ RunningOrdersBloc: API returned null or unsuccessful response');
        // Fallback to empty data if API fails
        emit(state.copyWith(
          orders: _sampleOrders,
          allOrders: _sampleOrders, // Store original empty orders
          status: RunningOrdersStatus.success,
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error fetching orders: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to fetch orders: $e',
      ));
    }
  }

  void _onFilterByCategory(
    FilterByCategory event,
    Emitter<RunningOrdersState> emit,
  ) {
    // Use allOrders for filtering to maintain original data
    final allOrders =
        state.allOrders.isNotEmpty ? state.allOrders : _sampleOrders;

    debugPrint('🍽️ RunningOrdersBloc: Filtering by ${event.category}');
    debugPrint(
        '🍽️ RunningOrdersBloc: Total orders available: ${allOrders.length}');

    // Debug order types
    final orderTypes = allOrders.map((order) => order["type"]).toSet();
    debugPrint('🍽️ RunningOrdersBloc: Available order types: $orderTypes');

    final filteredOrders = event.category == "All"
        ? allOrders
        : allOrders.where((order) => order["type"] == event.category).toList();

    debugPrint(
        '🍽️ RunningOrdersBloc: Filtering by ${event.category}, found ${filteredOrders.length} orders');

    emit(state.copyWith(
      orders: filteredOrders,
      selectedCategory: event.category,
    ));
  }

  void _onFilterByStatus(
    FilterByStatus event,
    Emitter<RunningOrdersState> emit,
  ) {
    // Use allOrders for filtering to maintain original data
    final allOrders =
        state.allOrders.isNotEmpty ? state.allOrders : _sampleOrders;

    final filteredOrders = event.statuses.isEmpty
        ? allOrders
        : allOrders
            .where((order) => event.statuses.contains(order["status"]))
            .toList();

    emit(state.copyWith(
      orders: filteredOrders,
      selectedStatuses: event.statuses,
    ));
  }

  void _onSortOrders(
    SortOrders event,
    Emitter<RunningOrdersState> emit,
  ) {
    final sortedOrders = List<Map<String, dynamic>>.from(state.orders);

    switch (event.sortBy) {
      case "newest":
        sortedOrders.sort((a, b) => b["time"].compareTo(a["time"]));
        break;
      case "oldest":
        sortedOrders.sort((a, b) => a["time"].compareTo(b["time"]));
        break;
      case "table":
        sortedOrders.sort((a, b) => a["table"].compareTo(b["table"]));
        break;
    }

    emit(state.copyWith(
      orders: sortedOrders,
      sortBy: event.sortBy,
    ));
  }

  Future<void> _onRefreshOrders(
    RefreshOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    emit(state.copyWith(status: RunningOrdersStatus.loading));

    try {
      debugPrint('🍽️ RunningOrdersBloc: Refreshing orders from API...');

      // Fetch fresh orders from the API
      final ordersResponse = await OrderService.getAllOrders(
        limit: 50, // Get more orders for better display
      );

      if (ordersResponse != null && ordersResponse.success) {
        debugPrint(
            '🍽️ RunningOrdersBloc: Successfully refreshed ${ordersResponse.data.data.length} orders');

        // Convert API response to legacy format for compatibility
        final legacyOrders = ordersResponse.data.data.map((orderDetail) {
          final legacyOrder = OrderService.convertToLegacyFormat(orderDetail);
          // Store the original OrderDetail for printing purposes
          legacyOrder['_originalOrderDetail'] = orderDetail;
          return legacyOrder;
        }).toList();

        // Filter out completed and cancelled orders for running orders page
        final activeOrders = legacyOrders.where((order) {
          final status = (order['status'] ?? '').toString().toUpperCase();
          return status != 'COMPLETED' && status != 'CANCELLED';
        }).toList();

        emit(state.copyWith(
          orders: activeOrders,
          allOrders:
              activeOrders, // Update allOrders during refresh with filtered orders
          status: RunningOrdersStatus.success,
        ));
      } else {
        debugPrint(
            '🍽️ RunningOrdersBloc: API returned null or unsuccessful response during refresh');
        // Fallback to empty data if API fails
        emit(state.copyWith(
          orders: _sampleOrders,
          allOrders: _sampleOrders, // Update allOrders during refresh fallback
          status: RunningOrdersStatus.success,
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error refreshing orders: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to refresh orders: $e',
      ));
    }
  }

  Future<void> _onUpdateOrderStatus(
    UpdateOrderStatus event,
    Emitter<RunningOrdersState> emit,
  ) async {
    try {
      debugPrint(
          '🍽️ RunningOrdersBloc: Updating order status for ${event.orderDetailId} to ${event.status}');

      final success = await OrderService.updateOrderStatus(
          event.orderDetailId, event.status);

      if (success) {
        debugPrint('🍽️ RunningOrdersBloc: Successfully updated order status');
        // Refresh the orders list to reflect the changes
        add(RefreshOrders());
      } else {
        debugPrint('🍽️ RunningOrdersBloc: Failed to update order status');
        emit(state.copyWith(
          status: RunningOrdersStatus.failure,
          error: 'Failed to update order status',
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error updating order status: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Error updating order status: $e',
      ));
    }
  }

  Map<String, dynamic>? getOrderForTable(String tableNumber) {
    // Get the current orders (either from API or sample data)
    final currentOrders =
        state.orders.isNotEmpty ? state.orders : _sampleOrders;

    try {
      return currentOrders.firstWhere(
        (order) =>
            order["table"] == tableNumber &&
            order["status"] != "Completed" &&
            order["status"] != "Served",
        orElse: () => {},
      );
    } catch (e) {
      debugPrint(
          '🍽️ RunningOrdersBloc: No active order found for table $tableNumber');
      return null;
    }
  }
}
